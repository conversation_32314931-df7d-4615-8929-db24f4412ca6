import {clsx} from 'clsx';
import {useEffect, useMemo, useState} from 'react';
import {
  Input,
  ListBox,
  ListBoxItem,
  Popover,
  Button as RACButton,
  Select,
  SelectValue,
} from 'react-aria-components';
import {useNavigate, useSearchParams} from 'react-router';

import {useMutation, useQuery} from '@apollo/client';
import {useDebouncedValue} from '@mantine/hooks';
import {ColDef, iconSetMaterial, themeQuartz} from 'ag-grid-enterprise';
import {AgGridReact, CustomCellRendererProps} from 'ag-grid-react';
import {format} from 'date-fns';
import {CheckIcon, ChevronDown, Hourglass, PlusIcon, XIcon} from 'lucide-react';
import {parseAsString, useQueryState} from 'nuqs';

import {SearchIconly} from '@/components/icons/SearchIconIconly.tsx';
import {useDialogState} from '@/components/modal-state-provider';
import {But<PERSON>} from '@/components/ui/button';
import {Tabs, TabsList, TabsTrigger} from '@/components/ui/tabs.tsx';
import {getAllStudyRequest, mutateStudyRequestItem} from '@/graphql/bookings.ts';
import {getProceduresList, getUnitsList} from '@/graphql/lists.ts';
import {ReferralDetailDrawer} from '@/views/bookings/components/ReferralDetailDrawer.tsx';
import RejectBookingDialogue from '@/views/bookings/components/RejectBookingDialog.tsx';

import {AddReferralDrawer} from './components/AddReferralDrawer';

function ReferalsAndOrders() {
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();
  const [searchValue, setSearchValue] = useQueryState('search', parseAsString.withDefault(''));
  const [debouncedSearch] = useDebouncedValue(searchValue, 300);
  const [, setRejectOpen] = useDialogState('reject-booking-dialogue');
  const [selectedUnit, setSelectedUnit] = useState();

  const {data: unitsData} = useQuery(getUnitsList);

  const {data: procedures} = useQuery(getProceduresList);
  const {
    data: studyData,
    loading,
    refetch,
  } = useQuery(getAllStudyRequest, {
    skip: !unitsData,
  });

  const filteredStudyData = useMemo(() => {
    if (!studyData?.study_request_item || !procedures?.procedure) {
      return [];
    }

    const targetUnitId = selectedUnit ?? Number(unitsData?.list_units[0]?.id);

    if (!targetUnitId) {
      return [];
    }

    return studyData.study_request_item
      .map((item) => {
        const itemProcedures =
          item?.procedure_ids
            ?.map((procedureId: number) => procedures.procedure.find((proc) => proc.id === procedureId))
            .filter(Boolean) || [];

        return {
          ...item,
          procedures: itemProcedures,
          procedure: itemProcedures[0] || null,
        };
      })
      .filter((item) => {
        return item.procedures.some((proc) => proc.unit_id === targetUnitId);
      });
  }, [studyData, procedures, selectedUnit, unitsData]);

  const [updateStudyRequestItem] = useMutation(mutateStudyRequestItem('status', 'accepted'));

  const currentTab = searchParams.get('status') || 'pending';

  const handleTabChange = (value: string) => {
    const newSearchParams = new URLSearchParams(searchParams);
    newSearchParams.set('status', value);
    setSearchParams(newSearchParams, {replace: true});
  };

  const groupedData = useMemo(() => {
    return filteredStudyData.reduce((acc: any, item: any) => {
      const studyRequestId = item.study_request.id;
      if (!acc[studyRequestId]) {
        acc[studyRequestId] = [];
      }
      acc[studyRequestId].push(item);
      return acc;
    }, {});
  }, [filteredStudyData]);

  const filteredData = Object.values(groupedData).reduce((acc: any[], items: any) => {
    const firstItem = items[0];
    const name = `${firstItem.study_request?.pas_pt?.pas_pt_names?.[0]?.firstname} ${firstItem.study_request?.pas_pt?.pas_pt_names?.[0]?.surname}`;
    const search = debouncedSearch.toLowerCase();
    const matchesSearch = name.toLowerCase().includes(search);

    if (!matchesSearch) return acc;

    const hasAcceptedItems = items.some((item: any) => item.status === 'accepted');
    const hasRejectedItems = items.some((item: any) => item.status === 'rejected');
    const hasPendingItems = items.some((item: any) => !item.status || item.status === 'pending');

    let shouldInclude;
    switch (currentTab) {
      case 'pending':
        shouldInclude = hasPendingItems;
        break;
      case 'accepted':
        shouldInclude = hasAcceptedItems;
        break;
      case 'rejected':
        shouldInclude = hasRejectedItems;
        break;
      default:
        shouldInclude = hasPendingItems;
    }

    if (shouldInclude) {
      return [...acc, ...items];
    }

    return acc;
  }, []);

  const getTabCount = (tab: string) => {
    return Object.values(groupedData).filter((items: any) => {
      const hasAcceptedItems = items.some((item: any) => item.status === 'accepted');
      const hasRejectedItems = items.some((item: any) => item.status === 'rejected');
      const hasPendingItems = items.some((item: any) => !item.status || item.status === 'pending');

      switch (tab) {
        case 'pending':
          return hasPendingItems;
        case 'accepted':
          return hasAcceptedItems;
        case 'rejected':
          return hasRejectedItems;
        default:
          return hasPendingItems;
      }
    }).length;
  };

  const PatientGroupCellRenderer = (params: CustomCellRendererProps & {[key: string]: any}) => {
    const [isExpanded, setIsExpanded] = useState(params.node.expanded || false);

    useEffect(() => {
      setIsExpanded(params.node.expanded);
      if (params?.node?.allLeafChildren?.length === 1) {
        params.node.setExpanded(false);
      }
    }, [params.node]);

    if (!params.node.group) return null;

    const groupData = params?.node?.key?.split('|');
    const [name]: any = groupData;

    const handleToggleExpansion = () => {
      const newExpandedState = !isExpanded;
      if (params?.node?.allLeafChildren?.length === 1) {
        params.node.setExpanded(false);
        setIsExpanded(false);
        return;
      }
      setIsExpanded(newExpandedState);
      params.node.setExpanded(newExpandedState);
    };

    return (
      <div
        className="flex h-9.5 items-center font-normal"
        onClick={handleToggleExpansion}
      >
        <div
          className={clsx(
            'mr-1.5 transform cursor-pointer text-neutral-500 transition-transform duration-200',
            isExpanded ? 'rotate-270' : 'rotate-360',
            params?.node?.allLeafChildren?.length === 1 && 'invisible'
          )}
        >
          <ChevronDown className="h-4 w-4 text-neutral-800" />
        </div>
        <div className="flex items-center text-xs text-neutral-800">
          <div className="font-semibold">{name}</div>
        </div>
      </div>
    );
  };

  const getRowClass = (params: any) => {
    if (!params.data) return '';
    return params.data && (params.data.status || 'pending') !== currentTab ? 'text-neutral-800' : '';
  };

  const columnDefs: ColDef[] = [
    {
      field: 'patientGroup',
      hide: true,
      headerName: 'Name',
      valueGetter: (params) => {
        if (!params.data) return params.node?.key || '';
        const item = params.data;
        return `${item?.study_request?.pas_pt?.pas_pt_names?.[0]?.firstname} ${item.study_request?.pas_pt?.pas_pt_names?.[0]?.surname}|${item.study_request?.type}|${format(
          new Date(item?.study_request?.pas_pt?.dob),
          'dd MMM yyyy'
        )}|${format(new Date(item.study_request.date_recieved), 'dd MMM yyyy')}|${item.study_request.id}`;
      },
      rowGroup: true,
    },
    {
      field: 'referralDate',
      headerName: 'Referral Date',
      valueGetter: (params) => {
        if (!params.data) return params.node?.key?.split('|')[3] || '';
        return format(new Date(params.data.study_request.date_recieved), 'dd MMM yyyy');
      },
      cellRenderer: (params: any) => {
        if (params.node.group) {
          return (
            <div className="flex h-9.5 items-center text-xs font-normal text-neutral-800">{params.value}</div>
          );
        }
        return null;
      },
    },
    {
      field: 'source',
      headerName: 'Source',
      valueGetter: (params) => {
        if (!params.data) return params.node?.key?.split('|')[1] || '';
        return params.data.study_request.type;
      },
      cellRenderer: (params: any) => {
        if (params.node.group) {
          return (
            <div className="flex h-9.5 items-center text-xs font-normal text-neutral-800 capitalize">
              {params.value}
            </div>
          );
        }
        return null;
      },
    },
    {
      field: 'procedures',
      headerName: 'Procedures',
      valueGetter: (params) => {
        if (!params.data) return '';

        return (
          params.data.procedures?.map((proc: any) => `${proc?.seq_number}. ${proc?.name}`).join(', ') || ''
        );
      },
      cellRenderer: (params: any) => {
        if (params.node.firstChild && params.node.lastChild && params.node.allChildrenCount !== 1) {
          return null;
        }
        if (params.node.group && params.node.allChildrenCount === 1) {
          const procedureNames =
            params.node.allLeafChildren[0].data.procedures
              ?.map((proc: any) => `${proc?.seq_number}. ${proc?.name}`)
              .join(', ') || '';
          return (
            <div className={'flex h-9.5 items-center text-xs font-normal text-neutral-800 capitalize'}>
              {procedureNames}
            </div>
          );
        }

        if (params.node.group) {
          return (
            <div
              className={
                'flex h-9.5 items-center text-[10px] font-semibold text-neutral-600 capitalize italic'
              }
            >
              {`${params.node.allLeafChildren.reduce((acc: any, item: any) => acc + (item.data.procedures?.length || 0), 0)} Procedures`}
            </div>
          );
        }

        return (
          <div
            className={clsx(
              'flex h-9.5 items-center text-xs font-normal text-neutral-800 capitalize',
              params.data && (params.data.status || 'pending') !== currentTab && 'opacity-50'
            )}
          >
            {params.value}
          </div>
        );
      },
    },
    {
      field: 'provProcedureDate',
      headerName: 'Prov. Procedure Date',
      valueGetter: (params) => {
        if (!params.data) return '';
        return params.data.provisional_procedure_date
          ? format(new Date(params.data.provisional_procedure_date), 'dd MMM yyyy')
          : '';
      },
      cellRenderer: (params: any) => {
        if (params.node.firstChild && params.node.lastChild && params.node.allChildrenCount !== 1) {
          return null;
        }

        if (params.node.group && params.node.allChildrenCount === 1) {
          return (
            <div className={'flex h-9.5 items-center text-xs font-normal text-neutral-800 capitalize'}>
              {format(
                new Date(params.node.allLeafChildren[0].data.provisional_procedure_date),
                'dd MMM yyyy'
              )}
            </div>
          );
        }

        return (
          <div
            className={clsx(
              'flex h-9.5 items-center text-xs font-normal text-neutral-800 capitalize',
              params.data && (params.data.status || 'pending') !== currentTab && 'opacity-50'
            )}
          >
            {params.value}
          </div>
        );
      },
    },
    {
      field: 'notes',
      headerName: 'Notes',
      valueGetter: (params) => {
        if (!params.data) return '';
        return params.data.study_request.appointment_note || params.data.note || '';
      },
      cellRenderer: (params: any) => {
        if (params.node.firstChild && params.node.lastChild && params.node.allChildrenCount !== 1) {
          return null;
        }

        if (params.node.group && params.node.allChildrenCount === 1) {
          return (
            <div
              className={'flex h-9.5 items-center truncate text-xs font-normal text-neutral-800 capitalize'}
            >
              {params.node.allLeafChildren[0].data.study_request.appointment_note ||
                params.node.allLeafChildren[0].data.note ||
                ''}
            </div>
          );
        }
        return (
          <div
            className={clsx(
              'flex h-9.5 items-center truncate text-xs font-normal text-neutral-800 capitalize',
              params.data && (params.data.status || 'pending') !== currentTab && 'opacity-50'
            )}
          >
            {params.value}
          </div>
        );
      },
      tooltipField: 'notes',
    },
    {
      field: 'rejection_reason',
      headerName: 'Rejection Reason',
      hide: currentTab !== 'rejected',
      valueGetter: (params) => {
        if (!params.data) return '';
        return params.data.rejection_reason || '';
      },
      cellRenderer: (params: any) => {
        if (params.node.firstChild && params.node.lastChild && params.node.allChildrenCount !== 1) {
          return null;
        }
        if (params.node.group && params.node.allChildrenCount === 1) {
          return (
            <div className={'flex h-9.5 items-center text-xs font-normal text-neutral-800 capitalize'}>
              {params.node.allLeafChildren[0].data.rejection_reason || ''}
            </div>
          );
        }
        return (
          <div
            className={clsx(
              'flex h-9.5 items-center text-xs font-normal text-neutral-800 capitalize',
              params.data && (params.data.status || 'pending') !== currentTab && 'opacity-50'
            )}
          >
            {params.value}
          </div>
        );
      },
      tooltipField: 'rejection_reason',
    },
    {
      field: 'status',
      headerName: '',
      valueGetter: (params) => {
        if (!params.data) return '';
        return params.data.status === 'accepted'
          ? 'Accepted'
          : params.data.status === 'rejected'
            ? 'Rejected'
            : 'Pending';
      },
      cellRenderer: (params: any) => {
        if (params.node.firstChild && params.node.lastChild && params.node.allChildrenCount !== 1) {
          return null;
        }
        if (params.node.group) {
          if (params.node.allChildrenCount === 1) {
            if (params.node.allLeafChildren[0].data.status === 'pending') {
              return (
                <div className="flex h-8.5 w-full items-center justify-end gap-0.5 text-xs font-semibold">
                  <RACButton
                    className="flex cursor-pointer items-center gap-0.5 rounded-sm border border-neutral-300 bg-white px-2 py-0.5 text-xs font-semibold text-[#16A34A] outline-none"
                    onPress={async () => {
                      await updateStudyRequestItem({
                        variables: {
                          id: params.node.allLeafChildren[0].data.id,
                          value: 'accepted',
                        },
                      });
                      await refetch();
                    }}
                  >
                    <CheckIcon className="h-4 w-4" />
                    Accept
                  </RACButton>
                  <RACButton
                    className="flex cursor-pointer items-center gap-0.5 rounded-sm border border-neutral-300 bg-white px-2 py-0.5 text-xs font-semibold text-red-500 outline-none"
                    onPress={() => {
                      const newSearchParams = new URLSearchParams(searchParams);
                      newSearchParams.set('reject', params.node.allLeafChildren[0].data.id.toString());
                      setSearchParams(newSearchParams, {replace: true});
                      setRejectOpen(true);
                    }}
                  >
                    <XIcon className="h-4 w-4" />
                    Reject
                  </RACButton>
                </div>
              );
            }
            if (params.node.allLeafChildren[0].data.status === 'accepted') {
              return (
                <div className="flex h-8.5 w-full items-center justify-end gap-0.5 text-xs font-semibold text-[#16A34A] capitalize">
                  <CheckIcon className="h-4 w-4" />
                  Actioned
                </div>
              );
            }
            if (params.node.allLeafChildren[0].data.status === 'rejected') {
              return (
                <div className="flex h-8.5 w-full items-center justify-end gap-0.5 text-xs font-semibold text-[#EF4444] capitalize">
                  <XIcon className="h-4 w-4" />
                  {params.node.allLeafChildren[0].data.status}
                </div>
              );
            }
          }
          return null;
        }

        const status = params?.data?.status || 'pending';
        const isActiveTab = currentTab === status;
        const statusClasses = clsx(
          'flex h-8.5 w-full items-center justify-end gap-0.5 text-xs font-semibold',
          !isActiveTab ? 'opacity-50 text-neutral-800' : 'opacity-100',
          {
            'text-[#16A34A]': status === 'accepted',
            'text-[#EF4444]': status === 'rejected',
            'text-neutral-800': status === 'pending',
          }
        );

        if (status === 'pending' && isActiveTab) {
          return (
            <div className={statusClasses}>
              <RACButton
                className="flex cursor-pointer items-center gap-0.5 rounded-sm border border-neutral-300 bg-white px-2 py-0.5 text-xs font-semibold text-[#16A34A] outline-none"
                onPress={async () => {
                  await updateStudyRequestItem({
                    variables: {id: params.data.id, value: 'accepted'},
                  });
                  await refetch();
                }}
              >
                <CheckIcon className="h-4 w-4" />
                Accept
              </RACButton>
              <RACButton
                className="flex cursor-pointer items-center gap-0.5 rounded-sm border border-neutral-300 bg-white px-2 py-0.5 text-xs font-semibold text-red-500 outline-none"
                onPress={() => {
                  const newSearchParams = new URLSearchParams(searchParams);
                  newSearchParams.set('reject', params.data.id.toString());
                  setSearchParams(newSearchParams, {replace: true});
                  setRejectOpen(true);
                }}
              >
                <XIcon className="h-4 w-4" />
                Reject
              </RACButton>
            </div>
          );
        }

        return (
          <div className={statusClasses}>
            {status === 'accepted' && <CheckIcon className="h-4 w-4" />}
            {status === 'rejected' && <XIcon className="h-4 w-4" />}
            {status === 'pending' && <Hourglass className="h-4 w-4" />}
            {status !== 'accepted' && status.charAt(0).toUpperCase() + status.slice(1)}
            {status === 'accepted' && 'Actioned'}
          </div>
        );
      },
    },
  ];

  const myTheme = themeQuartz.withPart(iconSetMaterial).withParams({
    fontFamily: 'Figtree, sans-serif',
    fontSize: 'var(--text-xs)',
    selectedRowBackgroundColor: 'color-mix(in oklab, var(--color-muted) 50%, transparent)',
    rowHoverColor: 'color-mix(in oklab, var(--color-muted) 50%, transparent)',
    backgroundColor: 'var(--color-neutral-100)',
    columnBorder: false,
    headerFontSize: 'var(--text-xs)',
    headerFontWeight: 600,
    headerTextColor: 'var(--color-neutral-800)',
    headerHeight: 48,
    iconSize: 13,
    rowHeight: 38,
    headerBackgroundColor: 'var(--color-white)',
    borderColor: 'var(--color-neutral-200)',
    textColor: 'var(--color-neutral-700)',
    rangeSelectionBorderColor: 'var(--color-brand2-500)',
    rangeHeaderHighlightColor: 'var(--color-brand2-500)',
    accentColor: 'var(--color-brand2-400)',
    rowBorder: true,
    selectCellBorder: true,
    wrapperBorder: true,
    borderRadius: 0,
    cellEditingShadow: false,
    selectCellBackgroundColor: 'var(--color-white)',
    cellHorizontalPadding: 12,
  });

  const defaultColDef = {
    resizable: true,
    suppressMovable: false,
    suppressHeaderFilterButton: true,
    suppressHeaderMenuButton: true,
    sortable: true,
    flex: 1,
  };

  const autoGroupColumnDef = {
    headerName: 'Name',
    cellRenderer: PatientGroupCellRenderer,
    cellRendererParams: {
      suppressCount: true,
    },
    minWidth: 250,
  };

  return (
    <>
      <div className="flex h-full flex-col gap-5">
        <div className="flex w-full items-center justify-between">
          <div className="flex w-full items-center gap-3">
            <Select
              aria-label="Departments"
              defaultSelectedKey={unitsData?.list_units[0]?.id ?? undefined}
              onSelectionChange={(s: any) => setSelectedUnit(s)}
            >
              <RACButton className="react-aria-Button h-9 w-50 rounded-sm">
                <SelectValue className="react-aria-SelectValue text-xs text-neutral-900 capitalize" />
                <ChevronDown />
              </RACButton>
              <Popover>
                <ListBox items={unitsData?.list_units}>
                  {(unit) => (
                    <ListBoxItem
                      className="react-aria-ListBoxItem text-sm"
                      id={unit.id}
                      textValue={String(unit.id)}
                    >
                      {unit.description}
                    </ListBoxItem>
                  )}
                </ListBox>
              </Popover>
            </Select>

            <div className="relative w-90">
              <Input
                placeholder="Search patients..."
                value={searchValue}
                onChange={(e) => setSearchValue(e.target.value)}
                className="peer ring-brand2-400/30 focus-visible:!border-brand2-500 h-9 w-full rounded-sm !border !border-neutral-300 bg-white px-4 pl-8 text-sm text-gray-600 outline-none placeholder:text-neutral-700 focus-visible:ring-3"
              />
              <div className="peer-focus:text-brand2-500 pointer-events-none absolute top-2 left-2 text-neutral-400">
                <SearchIconly className="h-4.5 w-4.5" />
              </div>
            </div>

            <Tabs
              value={currentTab}
              onValueChange={handleTabChange}
            >
              <TabsList className="flex w-fit justify-end rounded-sm border border-neutral-300 bg-neutral-100 p-1">
                <TabsTrigger
                  className="flex h-7 cursor-pointer gap-x-2 rounded-sm text-xs font-medium text-neutral-700 data-[state=active]:text-neutral-800"
                  value="pending"
                >
                  Pending ({getTabCount('pending')})
                </TabsTrigger>
                <TabsTrigger
                  className="flex h-7 cursor-pointer gap-x-2 rounded-sm text-xs font-medium text-neutral-700 data-[state=active]:text-neutral-800"
                  value="accepted"
                >
                  Actioned ({getTabCount('accepted')})
                </TabsTrigger>
                <TabsTrigger
                  className="flex h-7 cursor-pointer gap-x-2 rounded-sm text-xs font-medium text-neutral-700 data-[state=active]:text-neutral-800"
                  value="rejected"
                >
                  Rejected ({getTabCount('rejected')})
                </TabsTrigger>
              </TabsList>
            </Tabs>
          </div>
          <Button
            className="react-aria-Button ml-auto h-9 shrink-0 rounded-sm px-3"
            onPress={() => {
              const unitParam = selectedUnit ? `?unit=${selectedUnit}` : '';
              navigate(`/bookings/referrals/add${unitParam}`);
            }}
          >
            <PlusIcon
              strokeWidth={2.25}
              className="h-4 w-4"
            />
            Add referral
          </Button>
        </div>
        <div className="h-full w-full">
          <AgGridReact
            className="ag-referals"
            rowData={filteredData}
            loading={loading}
            columnDefs={columnDefs}
            getRowId={(params) => String(params.data.id)}
            getRowClass={getRowClass}
            autoGroupColumnDef={autoGroupColumnDef}
            groupDisplayType="singleColumn"
            groupDefaultExpanded={0}
            defaultColDef={defaultColDef}
            suppressAggFuncInHeader
            animateRows
            pagination={false}
            theme={myTheme}
            domLayout="normal"
            onCellClicked={(params) => {
              const newSearchParams = new URLSearchParams(searchParams);
              if (params.node.group && params.colDef.headerName !== 'Name') {
                navigate(
                  `/bookings/referrals/${params?.node?.key?.split('|').pop()}?${newSearchParams.toString()}`
                );
                return;
              }

              if (params?.colDef?.field === 'status') {
                return null;
              }
              navigate(
                `/bookings/referrals/${params?.data?.study_request?.id}?${newSearchParams.toString()}`
              );
            }}
            overlayNoRowsTemplate={`<div class="text-center p-8">
              <div class="text-lg font-semibold text-neutral-900 mb-2">No referrals found</div>
              <div class="text-sm text-neutral-600">Try refining your search or switch to a different tab. Currently showing ${currentTab} referrals.</div>
            </div>`}
          />
        </div>

        <RejectBookingDialogue />
        <AddReferralDrawer />
        <ReferralDetailDrawer />
      </div>
    </>
  );
}

export default ReferalsAndOrders;
