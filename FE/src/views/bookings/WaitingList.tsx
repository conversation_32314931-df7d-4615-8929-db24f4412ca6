import clsx from 'clsx';
import {Cell, Column, Input, Row, Table, TableHeader} from 'react-aria-components';
import {Button as RACButton} from 'react-aria-components';

import {useDebouncedValue} from '@mantine/hooks';
import {differenceInDays, format} from 'date-fns';
import {Calendar as CalendarIcon, PlusIcon} from 'lucide-react';
import {parseAsString, useQueryState} from 'nuqs';

import {LoadableTableBody} from '@/components/Table.tsx';
import {SearchIconly} from '@/components/icons/SearchIconIconly.tsx';
// import {useDialogState} from '@/components/modal-state-provider';
import {Button} from '@/components/ui/button';

function WaitingList() {
  const [searchValue, setSearchValue] = useQueryState('search', parseAsString.withDefault(''));
  const [debouncedSearch] = useDebouncedValue(searchValue, 300);
  // const [, setWaitingListOpen] = useDialogState('add-waiting-list-patient');
  // const [, setRejectRefferalOpen] = useDialogState('reject-referral');

  // Helper function to calculate days waiting
  const calculateDaysWaiting = (referralDate: string) => {
    const referral = new Date(referralDate);
    const today = new Date();
    return differenceInDays(today, referral);
  };

  const fakeData = [
    {
      id: 1,
      urgency: 'urgent',
      referralDate: '2024-04-10T08:30:00Z',
      procedure: 'RFT (Sp, LV)',
      name: 'Cheyenne Korsgaard',
      dob: '02 Feb 2016',
      source: 'Referral',
      provProcedureDate: '20 Mar 2023',
      notes: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit.',
      status: 'Pending',
    },
    {
      id: 8,
      urgency: 'urgent',
      referralDate: '2024-04-10T08:30:00Z',
      procedure: 'RFT (Sp, LV)',
      name: 'Cheyenne Korsgaard',
      dob: '02 Feb 2016',
      source: 'Referral',
      provProcedureDate: '20 Mar 2023',
      notes: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit.',
      status: 'Pending',
    },
    {
      id: 9,
      urgency: 'urgent',
      referralDate: '2024-04-10T08:30:00Z',
      procedure: 'RFT (Sp, LV)',
      name: 'Cheyenne Korsgaard',
      dob: '02 Feb 2016',
      source: 'Referral',
      provProcedureDate: '20 Mar 2023',
      notes: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit.',
      status: 'Pending',
    },
    {
      id: 2,
      urgency: 'soon',
      referralDate: '2024-02-12T14:15:00Z',
      procedure: 'RFT (Sp, LV)',
      name: 'Martin Donin',
      dob: '24 Jun 2012',
      source: 'E-request',
      provProcedureDate: '22 Jun 2015',
      notes: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit.',
      status: 'Added to waiting list',
    },
    {
      id: 3,
      urgency: 'standard',
      referralDate: '2024-04-24T10:45:00Z',
      procedure: 'RFT (Sp, LV)',
      name: 'James Philips',
      dob: '26 Apr 2016',
      source: 'Referral',
      provProcedureDate: '04 Aug 2008',
      notes: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit.',
      status: 'Booking scheduled',
    },
    {
      id: 4,
      urgency: 'standard',
      referralDate: '2024-12-09T16:20:00Z',
      procedure: 'RFT (Sp, LV)',
      name: 'Gretchen Bator',
      dob: '21 Sep 2009',
      source: 'Referral',
      provProcedureDate: '26 Dec 2010',
      notes: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit.',
      status: 'Rejected',
    },
    {
      id: 5,
      urgency: 'standard',
      referralDate: '2024-12-09T16:20:00Z',
      procedure: 'RFT (Sp, LV)',
      name: 'Gretchen Bator',
      dob: '21 Sep 2009',
      source: 'Referral',
      provProcedureDate: '26 Dec 2010',
      notes: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit.',
      status: 'Rejected',
    },
    {
      id: 6,
      urgency: 'standard',
      referralDate: '2024-12-09T16:20:00Z',
      procedure: 'RFT (Sp, LV)',
      name: 'Gretchen Bator',
      dob: '21 Sep 2009',
      source: 'Referral',
      provProcedureDate: '26 Dec 2010',
      notes: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit.',
      status: 'Rejected',
    },
    {
      id: 7,
      urgency: 'standard',
      referralDate: '2024-12-09T16:20:00Z',
      procedure: 'RFT (Sp, LV)',
      name: 'Gretchen Bator',
      dob: '21 Sep 2009',
      source: 'Referral',
      provProcedureDate: '26 Dec 2010',
      notes: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit.',
      status: 'Rejected',
    },
    // ... more rows as needed
  ];

  // Filter data by search (only by name)
  const filteredData = fakeData.filter((referral) => {
    const search = debouncedSearch.toLowerCase();
    return referral.name.toLowerCase().includes(search);
  });

  return (
    <div className="flex h-full flex-col gap-5">
      <div className="flex w-full items-center justify-between">
        <div className="relative w-90">
          <Input
            placeholder="Search patients..."
            value={searchValue}
            onChange={(e) => setSearchValue(e.target.value)}
            className="peer ring-brand2-400/30 focus-visible:!border-brand2-500 h-8 w-full rounded-sm !border !border-neutral-300 bg-white px-4 pl-8 text-sm text-gray-600 outline-none placeholder:text-neutral-700 focus-visible:ring-3"
          />

          <div className="peer-focus:text-brand2-500 pointer-events-none absolute top-1.75 left-2 text-neutral-400">
            <SearchIconly className="h-4.5 w-4.5" />
          </div>
        </div>

        <Button
          className="react-aria-Button ml-auto h-8 rounded-sm"
          onPress={() => {}}
        >
          <PlusIcon className="h-4 w-4" />
          Add referral
        </Button>
      </div>
      <Table
        aria-label="Contacts List"
        className="react-aria-Table has-[.table-empty]:grow"
      >
        <TableHeader>
          <Column>Urgency</Column>
          <Column>Days Waiting</Column>
          <Column isRowHeader>Referral date</Column>
          <Column>Procedure</Column>
          <Column>Name</Column>
          <Column>DOB</Column>
          <Column>Source</Column>
          <Column>Prov. procedure date</Column>
          <Column>Notes</Column>
          <Column></Column>
        </TableHeader>

        <LoadableTableBody
          emptyTitle="No contacts found"
          emptyDescription="Try refining your search or add a new contact"
          items={filteredData}
          isLoading={false}
          columnCount={10}
        >
          {(referral) => {
            const daysWaiting = calculateDaysWaiting(referral.referralDate);

            return (
              <Row
                key={referral.id}
                // onAction={() => {
                //   setSelectedDoctorId(referral.id as unknown as string);
                //   setEditContact(true);
                // }}
              >
                <Cell>
                  <div
                    className={clsx('inline-flex items-center rounded-sm px-2 py-1 text-xs capitalize', {
                      'bg-[#FEF5F4] text-[#FE0008]': referral.urgency === 'urgent',
                      'bg-[#FCF9EE] text-[#8D6F00]': referral.urgency === 'soon',
                      'bg-[#F7F7F7] text-[#787878]': referral.urgency === 'standard',
                    })}
                  >
                    {referral.urgency}
                  </div>
                </Cell>
                <Cell className="react-aria-Cell text-right text-sm">{daysWaiting}</Cell>
                <Cell className="react-aria-Cell text-sm">
                  {format(new Date(referral.referralDate), 'dd/MM/yyyy')}
                </Cell>
                <Cell>{referral.procedure}</Cell>
                <Cell>{referral.name}</Cell>
                <Cell>{referral.dob}</Cell>
                <Cell>{referral.source}</Cell>
                <Cell>{referral.provProcedureDate}</Cell>
                <Cell>{referral.notes}</Cell>
                <Cell>
                  <RACButton className="text-brand-500 hover:text-brand-400 flex cursor-pointer items-center gap-1 text-sm font-medium">
                    <CalendarIcon className="h-4 w-4" />
                    Schedule
                  </RACButton>
                </Cell>
              </Row>
            );
          }}
        </LoadableTableBody>
      </Table>
    </div>
  );
}

export default WaitingList;
