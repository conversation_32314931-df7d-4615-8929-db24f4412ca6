import {Suspense, lazy} from 'react';
import {ErrorBoundary} from 'react-error-boundary';
import {Navigate} from 'react-router';

import {ErrorFallback} from '@/ErrorBoundary.tsx';
import {StudiesRouter} from '@/features/studies';
import AuthenticatedViewLayout from '@/layouts/AuthenticatedViewLayout.tsx';

import RootLayout from './layouts/RootLayout.tsx';
import {AddReferralDrawer} from "@/views/bookings/components/AddReferralDrawer.tsx";
import {ReferralDetailDrawer} from "@/views/bookings/components/ReferralDetailDrawer.tsx";

const RftBreadcrumb = lazy(() => import('@/features/studies-rft/components/RftBreadcrumb'));
const TestDetailBreadcrumb = lazy(() => import('@/views/normal-values/components/TestDetailBreadcrumb'));

const PatientLayout = lazy(() => import('@/views/patients'));
const PatientsSearch = lazy(() => import('@/views/patients/patients-search.tsx'));
const PatientDetailBreadcrumb = lazy(() => import('./views/patients/components/PatientDetailBreadcrumb.tsx'));
const MFASetupPage = lazy(() => import('@/views/auth/MFASetup.tsx'));
const MFACodesPage = lazy(() => import('@/views/auth/MFACodes.tsx'));
const MFAChallenge = lazy(() => import('@/views/auth/MFAChallenge.tsx'));
const ResetPassword = lazy(() =>
  import('@/views/auth/ResetPassword.tsx').then((module) => ({default: module.ResetPassword}))
);

const NormalValuesTestDetailView = lazy(() => import('@/views/normal-values/library/test-equations.tsx'));
const NormalValuesTestListView = lazy(() => import('@/views/normal-values/library/test-lists.tsx'));

const LabTabs = lazy(() => import('./views/patients/patient-detail/LabTabs/LabTabs.tsx'));
const PatientTrendView = lazy(() => import('@/views/patients/patient-detail/patient-trend-view'));

const Dashboard = lazy(() => import('./views/dashboard'));

const PatientList = lazy(() => import('./views/patients/patients-list'));
const PatientDetail = lazy(() => import('./views/patients/patient-detail'));

const QualityControl = lazy(() => import('./views/quality-control/quality-control.tsx'));
const DevicesView = lazy(() => import('./views/quality-control/devices/devices-view'));
const ParametersView = lazy(() => import('./views/quality-control/parameters/parameters-view'));

const LoginPage = lazy(() => import('./views/auth/Login'));
const RegisterPage = lazy(() => import('./views/auth/Register'));
const ForgetPassPage = lazy(() => import('./views/auth/ForgetPass'));

const Contacts = lazy(() => import('./views/contacts/contacts'));

const PdfImport = lazy(() => import('./views/pdf-import/pdf-import'));
const Reports = lazy(() => import('./views/reports'));
const Preferences = lazy(() => import('./views/normal-values/preferences/preferences.tsx'));
const NormalValues = lazy(() => import('./views/normal-values/normal-values.tsx'));
const DispatchRecords = lazy(() => import('./views/dispatch-records/dispatch-records.tsx'));  


const BookingSchedule = lazy(() => import('./views/bookings/BookingSchedule'));
const WaitingList = lazy(() => import('./views/bookings/WaitingList'));
const ReferalsAndOrders = lazy(() => import('./views/bookings/ReferalsAndOrders'));

export const routes = [
  // Auth routes
  {
    path: '/auth/login',
    element: (
      <ErrorBoundary FallbackComponent={ErrorFallback}>
        <LoginPage />
      </ErrorBoundary>
    ),
  },
  {
    path: '/auth/register',
    element: (
      <ErrorBoundary FallbackComponent={ErrorFallback}>
        <RegisterPage />
      </ErrorBoundary>
    ),
  },
  {
    path: '/auth/forget-pass',
    element: (
      <ErrorBoundary FallbackComponent={ErrorFallback}>
        <ForgetPassPage />
      </ErrorBoundary>
    ),
  },
  {
    path: '/auth/mfa/setup',
    element: (
      <Suspense fallback={<span />}>
        <ErrorBoundary FallbackComponent={ErrorFallback}>
          <MFASetupPage />
        </ErrorBoundary>
      </Suspense>
    ),
  },
  {
    path: '/auth/mfa/codes',
    element: (
      <Suspense fallback={<span />}>
        <ErrorBoundary FallbackComponent={ErrorFallback}>
          <MFACodesPage />
        </ErrorBoundary>
      </Suspense>
    ),
  },
  {
    path: '/auth/mfa',
    element: (
      <Suspense fallback={<span />}>
        <ErrorBoundary FallbackComponent={ErrorFallback}>
          <MFAChallenge />
        </ErrorBoundary>
      </Suspense>
    ),
  },
  {
    path: '/auth/reset-pass',
    element: (
      <Suspense fallback={<span />}>
        <ErrorBoundary FallbackComponent={ErrorFallback}>
          <ResetPassword />
        </ErrorBoundary>
      </Suspense>
    ),
  },

  // Protected routes under RootLayout
  {
    path: '/',
    element: (
      <ErrorBoundary FallbackComponent={ErrorFallback}>
        <AuthenticatedViewLayout>
          <RootLayout />
        </AuthenticatedViewLayout>
      </ErrorBoundary>
    ),
    children: [
      {
        index: true,
        element: (
          <Navigate
            to="/patients"
            replace
          />
        ),
      },
      {
        path: '/data',
     
        handle: {
          crumbName: 'Data',
        },
        children: [     {
          index: true,
          element: (
            <Navigate
              to="dispatch-records"
              replace
            />
          ),
        },
          {
            path: 'dispatch-records',
            element: <DispatchRecords />,
            handle: {
              crumbName: 'Dispatch Records',
            },
          },
        ]
      },

      {
        path: 'dashboard',
        element: <Dashboard />,
        handle: {
          crumbName: 'Dashboard',
        },
      },
      {
        path: 'contacts',
        element: <Contacts />,
        handle: {
          crumbName: 'Contacts',
        },
      },
      {
        path: 'pdf-import',
        element: <PdfImport />,
        handle: {
          crumbName: 'MAGIC Import',
        },
      },
      {
        path: 'reports',
        handle: {
          crumbName: 'Reporting',
        },
        children: [
          {
            index: true,
            element: <Reports />,
          },
          {
            path: '*',
            handle: {
              dynamicBreadcrumb: (
                <Suspense fallback={<span />}>
                  <RftBreadcrumb />
                </Suspense>
              ),
              crumb: (data: unknown) => data,
              crumbName: 'Reporting Details',
            },
            element: <StudiesRouter />,
          },
        ],
      },
      {
        path: 'patients',
        handle: {
          crumbName: 'Patients',
        },
        children: [
          {
            index: true,
            element: (
              <Suspense fallback={<span />}>
                <PatientsSearch />
              </Suspense>
            ),
          },
          {
            path: 'all',
            element: <PatientList />,
          },
          {
            path: ':patientId',
            element: (
              <Suspense fallback={<span />}>
                <PatientLayout />
              </Suspense>
            ),
            handle: {
              dynamicBreadcrumb: (
                <Suspense fallback={<span />}>
                  <PatientDetailBreadcrumb />
                </Suspense>
              ),
              crumbName: 'Patient Details',
            },
            children: [
              {
                element: <PatientDetail />,
                children: [
                  {
                    index: true,
                    element: <LabTabs />,
                  },
                  {
                    path: 'trend',
                    element: (
                      <Suspense fallback={<span />}>
                        <PatientTrendView />
                      </Suspense>
                    ),
                  },
                ],
              },
              {
                path: '*',
                handle: {
                  dynamicBreadcrumb: (
                    <Suspense fallback={<span />}>
                      <RftBreadcrumb />
                    </Suspense>
                  ),
                  crumbPath: '/patients/:patientId',
                  crumb: (data: unknown) => data,
                },
                element: <StudiesRouter />,
              },
            ],
          },
        ],
      },
      {
        path: 'quality-control',
        handle: {
          crumbName: 'Quality Control',
          crumbPath: '/quality-control',
        },
        element: <QualityControl />,
        children: [
          {
            index: true,
            element: (
              <Navigate
                to="devices"
                replace
              />
            ),
          },
          {
            path: 'devices/:labId/:deviceId/:controlEquipmentId',
            handle: {
              crumbName: 'Device',
              crumbPath: '/quality-control/devices/:labId/:deviceId/:controlEquipmentId',
            },
            element: <DevicesView />,
          },
          {
            path: 'parameters/:labId/:parameterId/:controlId',
            handle: {
              crumbName: 'Parameter',
              crumbPath: '/quality-control/parameters/:labId/:parameterId/:controlId',
            },
            element: <ParametersView />,
          },
          {
            path: 'devices',
            handle: {
              crumbName: 'Devices',
              crumbPath: '/quality-control/devices',
            },
            element: <DevicesView />,
          },
          {
            path: 'parameters',
            handle: {
              crumbName: 'Parameters',
              crumbPath: '/quality-control/parameters',
            },
            element: <ParametersView />,
          },
        ],
      },
      {
        path: 'normal-values',
        handle: {
          crumbName: 'Normal Values',
          crumbPath: '/normal-values',
        },
        element: <NormalValues />,
        children: [
          {
            index: true,
            element: (
              <Navigate
                to="library"
                replace
              />
            ),
          },
          {
            path: 'preferences',
            handle: {
              crumbName: 'Preferences',
              crumbPath: '/normal-values/preferences',
            },
            element: <Preferences />,
          },
          {
            path: 'library',
            handle: {
              crumbName: 'Library',
              crumbPath: '/normal-values/library',
            },
            children: [
              {
                index: true,
                element: <NormalValuesTestListView />,
              },
              {
                path: ':testId/:equationId?',
                handle: {
                  dynamicBreadcrumb: (
                    <Suspense fallback={<span />}>
                      <TestDetailBreadcrumb />
                    </Suspense>
                  ),
                  crumbName: 'Equations',
                  crumbPath: '/normal-values/library/:testId',
                },
                element: <NormalValuesTestDetailView />,
              },
              {
                path: ':testId/source/:sourceId',
                handle: {
                  dynamicBreadcrumb: (
                    <Suspense fallback={<span />}>
                      <TestDetailBreadcrumb />
                    </Suspense>
                  ),
                  crumbName: 'Source',
                  crumbPath: '/normal-values/library/:testId/source/:sourceId',
                },
                element: <NormalValuesTestDetailView />,
              },
            ],
          },
        ],
      },
      {
        path: 'bookings',
        handle: {
          crumbName: 'Bookings',
        },
        children: [
          {
            path: 'referrals',
            element: <ReferalsAndOrders/>,
            handle: { crumbName: 'Referrals' },
            children: [
              {
                path: 'add',
                element: <AddReferralDrawer />,
              },
              {
                path: ':studyRequestId',
                element: <ReferralDetailDrawer />,
              },
            ]
          },
          {
            path: 'waiting-list',
            element: <WaitingList />,
            handle: { crumbName: 'Waiting List' },
          },
          {
            path: 'schedules',
            element: <BookingSchedule />,
            handle: { crumbName: 'Booking Schedules' },
          },
        ],
      },
    ],
  },
];
