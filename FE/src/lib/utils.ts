import {type ClassValue, clsx} from 'clsx';

import {twMerge} from 'tailwind-merge';
import {timezoneMapping} from '@/lib/countries.ts';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}


interface PersonWithName {
  title?: string | null;
  surname?: string | null;
  firstname?: string | null;
}

export function formatPersonName(person: PersonWithName) {
  let name = '';
  if (person.title) {
    name += person.title + '. ';
  }

  if (person.firstname) {
    name += person.firstname+ ', ';
  }

  if (person.surname) {
    name += person.surname;
  }

  return name.trim();
}

export function getCountryFromTimezone() {
  const userTimeZone = Intl.DateTimeFormat().resolvedOptions().timeZone;
  for (const countryData of Object.values(timezoneMapping.countries)) {
    if (countryData.zones.includes(userTimeZone)) {
      return countryData.name;
    }
  }
  return 'Unknown';
}