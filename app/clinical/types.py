from abc import abstractmethod
from enum import StrEnum
from functools import cached_property
from typing import Optional, Iterable

import math

from app import db
from app.clinical.models import PredRefParameters, RSession
from app.normal_values.utils import BaseEquationMetadata, EquationPredResult, RftGender, Ethnicities
from app.normal_values.views import normal_values_for_parameters
from app.pdf_convert.models import PasPt


class RefType(StrEnum):
    NO_REF = 'no_ref'
    RANGE = 'range'
    ULN = 'uln'
    LLN = 'lln'


class ClinicalModule:
    metadata: 'BaseEquationMetadata'
    _config_tests = []

    @cached_property
    def normal_values(self) -> dict[int, Optional['EquationPredResult']]:
        """Calculate normal values for all parameters in this test."""
        param_ids = self.get_parameter_ids()

        return normal_values_for_parameters(
            param_ids=param_ids,
            metadata=self.metadata,
            test_date=self.get_test_session().testdate,
        )

    @cached_property
    def metadata(self) -> BaseEquationMetadata:
        """Calculate metadata from patient and session data."""
        patient = self.get_patient()
        session = self.get_test_session()

        if not patient:
            raise ValueError("Patient data not available for metadata calculation.")

        return BaseEquationMetadata(
            age=patient.age_at_date(session.testdate),
            ht=float(session.height or '0.0'),
            wt=float(session.weight or '0.0'),
            gender=RftGender.from_code(patient.gender_forrfts_code),
            ethnicity=Ethnicities(int(patient.race_forrfts_code or 1)),
        )

    @abstractmethod
    def get_test_session(self) -> 'RSession':
        ...

    @abstractmethod
    def get_patient(self) -> 'PasPt':
        ...

    @abstractmethod
    def get_parameter_ids(self) -> Iterable[int]:
        ...

    @abstractmethod
    def get_db_value(self, field_name: str) -> str | float | None:
        ...

    @classmethod
    def get_config(cls):
        tests = {
            test.name: {
                name: {
                    'param': {
                        'description': test_param.parameter.description,
                        'testid': test_param.parameter.testid,
                        'longname': test_param.parameter.longname,
                        'units': test_param.parameter.units,
                        'units_si': test_param.parameter.units_si,
                        'units_convert_trad_to_si': test_param.parameter.units_convert_trad_to_si,
                        'decimalplaces': test_param.parameter.decimalplaces,
                        'decimalplaces_si': test_param.parameter.decimalplaces_si,
                    },
                    'db_field': test_param.db_field,
                    'ref_type': test_param.ref_type
                }
                for name, test_param in test._fields.items()
                if isinstance(test_param, TestParameter)
            }
            for test in cls._config_tests
            if test.name is not None
        }

        return {
            'tests': tests,
        }


class DbFieldValue:
    def __init__(self, db_field: str = None, module: 'ClinicalModule' = None):
        self.db_field = db_field
        self.clinical_module = module

    @property
    def value(self):
        """Get the value from the database field."""
        if self.clinical_module is None:
            raise ValueError("ClinicalModule instance not set.")

        val = self.clinical_module.get_db_value(self.db_field)
        try:
            return float(val)
        except (ValueError, TypeError):
            return val


class TestParameter(DbFieldValue):
    def __init__(
            self,
            param_name: str,
            db_field: str,
            module: 'ClinicalModule' = None,
            ref_type: RefType = RefType.NO_REF,
    ):
        super().__init__(db_field, module)
        self.param_name = param_name
        self.ref_type = ref_type

    @property
    def formated_value(self):
        value = super().value
        if self.parameter:
            return self.parameter.format_value(value)
        return value

    @property
    def pred_result(self) -> Optional[EquationPredResult]:
        """Return pred result from parent test normal values."""
        if self.clinical_module is None:
            raise ValueError("rft_module test not set.")

        normal_values = self.clinical_module.normal_values
        if normal_values and self.parameter.id in normal_values:
            return normal_values[self.parameter.id]
        return None

    @property
    def zscore(self):
        if self.pred_result is None or self.value is None:
            return '-'

        try:
            zscore_value = self.pred_result.get_zscore(self.value)
            if zscore_value is None or math.isnan(zscore_value):
                return '-'
            return "{:.2f}".format(zscore_value)
        except (ValueError, ZeroDivisionError, TypeError):
            return '-'

    @property
    def pred_percentage(self):
        if self.pred_result is None or self.value is None:
            return '-'

        try:
            pred_percentage_value = self.pred_result.get_pred_percentage(self.value)
            if pred_percentage_value is None:
                return '-'
            return "{:.0f}%".format(pred_percentage_value)
        except (ValueError, ZeroDivisionError, TypeError):
            return '-'

    @cached_property
    def parameter(self) -> PredRefParameters:
        """Lazy load the parameter from database."""
        param = db.session.query(PredRefParameters).filter_by(description=self.param_name).first()
        if not param:
            raise ValueError(f"Parameter with name {self.param_name} not found.")

        return param

    @property
    def ref_value(self):
        """Get the reference value for this parameter."""
        if self.pred_result is None or self.parameter is None:
            return '-'

        if self.ref_type == RefType.NO_REF:
            return None
        if self.ref_type == RefType.LLN:
            return f"≥ {self.pred_result.lln:.{self.parameter.decimalplaces}f}"
        if self.ref_type == RefType.ULN:
            return f"≥ {self.pred_result.uln:.{self.parameter.decimalplaces}f}"
        if self.ref_type == RefType.RANGE:
            lln = self.pred_result.lln or self.pred_result.range[0]
            uln = self.pred_result.uln or self.pred_result.range[1]

            return f"{lln:.{self.parameter.decimalplaces}f} - {uln:.{self.parameter.decimalplaces}f}"

        return '-'
